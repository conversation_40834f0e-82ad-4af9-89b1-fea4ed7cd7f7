[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> zhengshi/internal/router.NewRouter.func1 (4 handlers)
[GIN-debug] POST   /api/v1/image/analyze     --> zhengshi/internal/handler.(*ImageHandler).AnalyzeImage-fm (4 handlers)
[GIN-debug] GET    /api/v1/image/analysis/:id --> zhengshi/internal/handler.(*ImageHandler).GetAnalysisResult-fm (4 handlers)
{"level":"INFO","timestamp":"2025-06-13T21:44:23.720+0800","caller":"utils/logger.go:82","msg":"Server starting on port :8080"}
{"level":"INFO","timestamp":"2025-06-13T21:44:24.545+0800","caller":"utils/logger.go:82","msg":"Request completed","method":"GET","path":"/health","status":200,"latency":"142.5µs","client_ip":"::1","user_agent":"curl/8.7.1"}
{"level":"INFO","timestamp":"2025-06-13T21:44:33.509+0800","caller":"utils/logger.go:82","msg":"Image analysis request received","trace_id":"dalg872nhqqg","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:44:33.580+0800","caller":"utils/logger.go:82","msg":"Image validation passed","trace_id":"dalg872nhqqg","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:44:33.580+0800","caller":"utils/logger.go:82","msg":"Process context created with image URL","trace_id":"dalg872nhqqg","image_url":"http://img.igmdns.com/img/ca0001.jpg","status":"received","current_step":"请求已接收"}
{"level":"INFO","timestamp":"2025-06-13T21:44:33.580+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis","trace_id":"dalg872nhqqg","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:44:33.580+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis","trace_id":"dalg872nhqqg","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:44:33.580+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis","trace_id":"dalg872nhqqg","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:44:33.580+0800","caller":"utils/logger.go:82","msg":"Sending request to Qwen API","url":"https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation","model":"qwen-vl-plus","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:44:35.612+0800","caller":"utils/logger.go:82","msg":"Qwen DashScope API request completed successfully","image_url":"http://img.igmdns.com/img/ca0001.jpg","tokens_used":1029,"request_id":"7de1f9de-3c31-9c38-9115-5dc188805607","content_length":321}
{"level":"INFO","timestamp":"2025-06-13T21:44:35.613+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed","trace_id":"dalg872nhqqg","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:44:35.613+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed successfully","trace_id":"dalg872nhqqg","image_url":"http://img.igmdns.com/img/ca0001.jpg","result_length":321}
{"level":"INFO","timestamp":"2025-06-13T21:44:35.613+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed, raw data saved to ProcessContext","trace_id":"dalg872nhqqg","image_url":"http://img.igmdns.com/img/ca0001.jpg","qwen_data_length":321}
{"level":"INFO","timestamp":"2025-06-13T21:44:35.613+0800","caller":"utils/logger.go:82","msg":"Request completed","method":"POST","path":"/api/v1/image/analyze","status":200,"latency":"2.103942083s","client_ip":"::1","user_agent":"curl/8.7.1"}
{"level":"INFO","timestamp":"2025-06-13T21:48:18.360+0800","caller":"utils/logger.go:82","msg":"Image analysis request received","trace_id":"dalgb2d9u1p4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:48:18.467+0800","caller":"utils/logger.go:82","msg":"Image validation passed","trace_id":"dalgb2d9u1p4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:48:18.468+0800","caller":"utils/logger.go:82","msg":"Process context created with image URL","trace_id":"dalgb2d9u1p4","image_url":"http://img.igmdns.com/img/ca0001.jpg","status":"received","current_step":"请求已接收"}
{"level":"INFO","timestamp":"2025-06-13T21:48:18.468+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis","trace_id":"dalgb2d9u1p4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:48:18.468+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis","trace_id":"dalgb2d9u1p4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:48:18.468+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis","trace_id":"dalgb2d9u1p4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:48:18.468+0800","caller":"utils/logger.go:82","msg":"Sending request to Qwen API","url":"https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation","model":"qwen-vl-plus","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:48:21.654+0800","caller":"utils/logger.go:82","msg":"Qwen DashScope API request completed successfully","image_url":"http://img.igmdns.com/img/ca0001.jpg","tokens_used":1029,"request_id":"3ae2052c-0969-9191-b548-f399a90230a8","content_length":321}
{"level":"INFO","timestamp":"2025-06-13T21:48:21.654+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed","trace_id":"dalgb2d9u1p4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T21:48:21.654+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed successfully","trace_id":"dalgb2d9u1p4","image_url":"http://img.igmdns.com/img/ca0001.jpg","result_length":321}
{"level":"INFO","timestamp":"2025-06-13T21:48:21.654+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed, raw data saved to ProcessContext","trace_id":"dalgb2d9u1p4","image_url":"http://img.igmdns.com/img/ca0001.jpg","qwen_data_length":321}
{"level":"INFO","timestamp":"2025-06-13T21:48:21.654+0800","caller":"utils/logger.go:82","msg":"Request completed","method":"POST","path":"/api/v1/image/analyze","status":200,"latency":"3.294355458s","client_ip":"::1","user_agent":"PostmanRuntime-ApipostRuntime/1.1.0"}
{"level":"INFO","timestamp":"2025-06-13T21:50:00.864+0800","caller":"utils/logger.go:82","msg":"Shutting down server..."}
{"level":"INFO","timestamp":"2025-06-13T21:50:00.864+0800","caller":"utils/logger.go:82","msg":"Server exited"}
