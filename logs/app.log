[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> zhengshi/internal/router.NewRouter.func1 (4 handlers)
[GIN-debug] POST   /api/v1/image/analyze     --> zhengshi/internal/handler.(*ImageHandler).AnalyzeImage-fm (4 handlers)
[GIN-debug] GET    /api/v1/image/analysis/:id --> zhengshi/internal/handler.(*ImageHandler).GetAnalysisResult-fm (4 handlers)
{"level":"INFO","timestamp":"2025-06-13T22:33:52.130+0800","caller":"utils/logger.go:82","msg":"Server starting on port :8080"}
{"level":"INFO","timestamp":"2025-06-13T22:33:52.953+0800","caller":"utils/logger.go:82","msg":"Request completed","method":"GET","path":"/health","status":200,"latency":"99µs","client_ip":"::1","user_agent":"curl/8.7.1"}
{"level":"INFO","timestamp":"2025-06-13T22:34:03.007+0800","caller":"utils/logger.go:82","msg":"Image analysis request received","trace_id":"dalha38pr3gw","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:34:04.097+0800","caller":"utils/logger.go:82","msg":"Image validation passed","trace_id":"dalha38pr3gw","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:34:04.097+0800","caller":"utils/logger.go:82","msg":"Process context created with image URL","trace_id":"dalha38pr3gw","image_url":"http://img.igmdns.com/img/ca0001.jpg","status":"received","current_step":"请求已接收"}
{"level":"INFO","timestamp":"2025-06-13T22:34:04.097+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis and data parsing","trace_id":"dalha38pr3gw","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:34:04.097+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis and parsing","trace_id":"dalha38pr3gw","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:34:04.097+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis","trace_id":"dalha38pr3gw","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:34:04.097+0800","caller":"utils/logger.go:82","msg":"Sending request to Qwen API","url":"https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation","model":"qwen-vl-plus","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Qwen DashScope API request completed successfully","image_url":"http://img.igmdns.com/img/ca0001.jpg","tokens_used":1029,"request_id":"733011ee-8d6b-9167-a330-bc5469f4a9dc","response_size":743}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed","trace_id":"dalha38pr3gw","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed, starting data parsing","trace_id":"dalha38pr3gw","image_url":"http://img.igmdns.com/img/ca0001.jpg","raw_data_length":743}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Starting Qwen data parsing","trace_id":"dalha38pr3gw","raw_data_length":743}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Qwen content parsed successfully","trace_id":"dalha38pr3gw","qutext":"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Quest type extracted successfully","trace_id":"dalha38pr3gw","quest_type":"单选题"}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Options validated successfully","trace_id":"dalha38pr3gw","quest_type":"单选题","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Quest content cleaned successfully","trace_id":"dalha38pr3gw","original_qutext":"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？","cleaned_content":"如图所示，驾车遇到此情况时应当注意什么？"}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Qwen data parsing completed successfully","trace_id":"dalha38pr3gw","quest_type":"单选题","quest_content":"如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Qwen data parsing completed successfully","trace_id":"dalha38pr3gw","image_url":"http://img.igmdns.com/img/ca0001.jpg","quest_type":"单选题","quest_content":"如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.069+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis and data parsing completed successfully","trace_id":"dalha38pr3gw","image_url":"http://img.igmdns.com/img/ca0001.jpg","quest_type":"单选题","quest_content":"如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:34:06.070+0800","caller":"utils/logger.go:82","msg":"Request completed","method":"POST","path":"/api/v1/image/analyze","status":200,"latency":"3.063318375s","client_ip":"::1","user_agent":"curl/8.7.1"}
{"level":"INFO","timestamp":"2025-06-13T22:36:11.114+0800","caller":"utils/logger.go:82","msg":"Image analysis request received","trace_id":"dalhbq3derdc","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:36:11.202+0800","caller":"utils/logger.go:82","msg":"Image validation passed","trace_id":"dalhbq3derdc","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:36:11.202+0800","caller":"utils/logger.go:82","msg":"Process context created with image URL","trace_id":"dalhbq3derdc","image_url":"http://img.igmdns.com/img/ca0001.jpg","status":"received","current_step":"请求已接收"}
{"level":"INFO","timestamp":"2025-06-13T22:36:11.202+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis and data parsing","trace_id":"dalhbq3derdc","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:36:11.202+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis and parsing","trace_id":"dalhbq3derdc","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:36:11.202+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis","trace_id":"dalhbq3derdc","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:36:11.202+0800","caller":"utils/logger.go:82","msg":"Sending request to Qwen API","url":"https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation","model":"qwen-vl-plus","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Qwen DashScope API request completed successfully","image_url":"http://img.igmdns.com/img/ca0001.jpg","tokens_used":1029,"request_id":"31e4efdb-93d6-9a26-98c4-469a17b2a94a","response_size":743}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed","trace_id":"dalhbq3derdc","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed, starting data parsing","trace_id":"dalhbq3derdc","image_url":"http://img.igmdns.com/img/ca0001.jpg","raw_data_length":743}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Starting Qwen data parsing","trace_id":"dalhbq3derdc","raw_data_length":743}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Qwen content parsed successfully","trace_id":"dalhbq3derdc","qutext":"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Quest type extracted successfully","trace_id":"dalhbq3derdc","quest_type":"单选题"}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Options validated successfully","trace_id":"dalhbq3derdc","quest_type":"单选题","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Quest content cleaned successfully","trace_id":"dalhbq3derdc","original_qutext":"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？","cleaned_content":"如图所示，驾车遇到此情况时应当注意什么？"}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Qwen data parsing completed successfully","trace_id":"dalhbq3derdc","quest_type":"单选题","quest_content":"如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Qwen data parsing completed successfully","trace_id":"dalhbq3derdc","image_url":"http://img.igmdns.com/img/ca0001.jpg","quest_type":"单选题","quest_content":"如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis and data parsing completed successfully","trace_id":"dalhbq3derdc","image_url":"http://img.igmdns.com/img/ca0001.jpg","quest_type":"单选题","quest_content":"如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:36:13.077+0800","caller":"utils/logger.go:82","msg":"Request completed","method":"POST","path":"/api/v1/image/analyze","status":200,"latency":"1.963149209s","client_ip":"::1","user_agent":"curl/8.7.1"}
{"level":"INFO","timestamp":"2025-06-13T22:45:51.306+0800","caller":"utils/logger.go:82","msg":"Image analysis request received","trace_id":"dalhj4movdr4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:45:51.595+0800","caller":"utils/logger.go:82","msg":"Image validation passed","trace_id":"dalhj4movdr4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:45:51.595+0800","caller":"utils/logger.go:82","msg":"Process context created with image URL","trace_id":"dalhj4movdr4","image_url":"http://img.igmdns.com/img/ca0001.jpg","status":"received","current_step":"请求已接收"}
{"level":"INFO","timestamp":"2025-06-13T22:45:51.595+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis and data parsing","trace_id":"dalhj4movdr4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:45:51.595+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis and parsing","trace_id":"dalhj4movdr4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:45:51.595+0800","caller":"utils/logger.go:82","msg":"Starting Qwen image analysis","trace_id":"dalhj4movdr4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:45:51.595+0800","caller":"utils/logger.go:82","msg":"Sending request to Qwen API","url":"https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation","model":"qwen-vl-plus","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Qwen DashScope API request completed successfully","image_url":"http://img.igmdns.com/img/ca0001.jpg","tokens_used":1029,"request_id":"4bbaf8ed-147b-9783-8081-71e729339c28","response_size":743}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed","trace_id":"dalhj4movdr4","image_url":"http://img.igmdns.com/img/ca0001.jpg"}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis completed, starting data parsing","trace_id":"dalhj4movdr4","image_url":"http://img.igmdns.com/img/ca0001.jpg","raw_data_length":743}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Starting Qwen data parsing","trace_id":"dalhj4movdr4","raw_data_length":743}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Qwen content parsed successfully","trace_id":"dalhj4movdr4","qutext":"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Quest type extracted successfully","trace_id":"dalhj4movdr4","quest_type":"单选题"}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Options validated successfully","trace_id":"dalhj4movdr4","quest_type":"单选题","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Quest content cleaned successfully","trace_id":"dalhj4movdr4","original_qutext":"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？","cleaned_content":"如图所示，驾车遇到此情况时应当注意什么？"}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Qwen data parsing completed successfully","trace_id":"dalhj4movdr4","quest_type":"单选题","quest_content":"如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Qwen data parsing completed successfully","trace_id":"dalhj4movdr4","image_url":"http://img.igmdns.com/img/ca0001.jpg","quest_type":"单选题","quest_content":"如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Qwen image analysis and data parsing completed successfully","trace_id":"dalhj4movdr4","image_url":"http://img.igmdns.com/img/ca0001.jpg","quest_type":"单选题","quest_content":"如图所示，驾车遇到此情况时应当注意什么？","options_count":4}
{"level":"INFO","timestamp":"2025-06-13T22:45:53.644+0800","caller":"utils/logger.go:82","msg":"Request completed","method":"POST","path":"/api/v1/image/analyze","status":200,"latency":"2.338009875s","client_ip":"::1","user_agent":"curl/8.7.1"}
