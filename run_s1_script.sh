#!/bin/bash

# S1脚本启动器
# 用于启动多线程拍照搜题API测试脚本

echo "=== S1 多线程拍照搜题脚本启动器 ==="
echo "开始时间: $(date)"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: Go环境未安装或未配置到PATH"
    exit 1
fi

# 检查当前目录是否为项目根目录
if [ ! -f "go.mod" ]; then
    echo "错误: 请在项目根目录下运行此脚本"
    exit 1
fi

# 确保logs目录存在
mkdir -p logs

# 清理之前的日志文件（可选）
read -p "是否清理之前的日志文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "清理日志文件..."
    rm -f logs/cache_key.log
    rm -f logs/quest_content.log
    echo "日志文件已清理"
fi

echo ""
echo "=== 脚本配置 ==="
echo "API地址: http://localhost:8080"
echo "并发线程数: 10"
echo "处理范围: 0001-1000"
echo "图片URL模板: http://img.igmdns.com/img/ca{序号}.jpg"
echo "Cache Key日志: logs/cache_key.log"
echo "Quest Content日志: logs/quest_content.log"
echo ""

# 确认启动
read -p "确认启动脚本? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "脚本已取消"
    exit 0
fi

echo ""
echo "=== 启动S1脚本 ==="

# 运行脚本
go run cmd/s1_script/main.go

# 检查执行结果
if [ $? -eq 0 ]; then
    echo ""
    echo "=== 脚本执行完成 ==="
    echo "结束时间: $(date)"
    echo ""
    echo "日志文件位置:"
    echo "- Cache Key日志: logs/cache_key.log"
    echo "- Quest Content日志: logs/quest_content.log"
    echo ""
    
    # 显示日志文件统计
    if [ -f "logs/cache_key.log" ]; then
        cache_lines=$(wc -l < logs/cache_key.log)
        echo "Cache Key日志行数: $cache_lines"
    fi
    
    if [ -f "logs/quest_content.log" ]; then
        content_lines=$(wc -l < logs/quest_content.log)
        echo "Quest Content日志行数: $content_lines"
    fi
else
    echo ""
    echo "=== 脚本执行失败 ==="
    echo "请检查错误信息并重试"
    exit 1
fi
