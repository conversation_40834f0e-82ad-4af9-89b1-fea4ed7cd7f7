server:
  port: ":8080"
  mode: "debug"  # debug, release

database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "your_mysql_password"
  database: "zhengshi"
  charset: "utf8mb4"

redis:
  host: "localhost"
  port: 6379
  password: "your_redis_password"
  db: 0

log:
  level: "info"  # debug, info, warn, error, fatal
  output: "stdout"  # stdout or file path like "./logs/app.log"

ai:
  qwen:
    api_key: "your_qwen_api_key_here"
    base_url: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
    model: "qwen-vl-plus"
    timeout: 30
  
  deepseek:
    api_key: "your_deepseek_api_key_here"
    base_url: "https://api.deepseek.com/v1/chat/completions"
    model: "deepseek-chat"
    timeout: 30
