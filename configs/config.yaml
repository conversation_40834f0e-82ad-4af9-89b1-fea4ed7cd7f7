server:
  port: ":8080"
  mode: "debug"  # debug, release

database:
  host: "***********"
  port: 3380
  username: "gmdns"
  password: "Suyan15913.."
  database: "t_solve_go_api"
  charset: "utf8mb4"

redis:
  host: "***********"
  port: 6379
  password: "Suyan15913.."
  db: 0

log:
  level: "info"  # debug, info, warn, error, fatal
  output: "stdout"  # stdout or file path

ai:
  qwen:
    api_key: "sk-21d8cdf3309e462cbf9391a4963862f0"
    base_url: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
    model: "qwen-vl-plus"
    timeout: 30

  deepseek:
    api_key: "***********************************"
    base_url: "https://api.deepseek.com/v1/chat/completions"
    model: "deepseek-chat"
    timeout: 30
