# Zhengshi - 智能图片分析API服务

基于Go + Gin框架开发的智能图片分析API服务，集成Qwen和DeepSeek AI模型，提供高性能的图片内容分析和深度解读功能。

## 功能特性

- 🖼️ **图片智能分析**: 支持通过URL提交图片进行AI分析
- 🤖 **双模型协作**: 集成Qwen视觉模型和DeepSeek文本模型
- 📊 **结构化数据**: 格式化解析AI响应，提供结构化分析结果
- 🚀 **高性能缓存**: Redis缓存机制，避免重复计算
- 📝 **完整日志**: 详细的调试日志和错误追踪
- 🔧 **标准化开发**: 规范的目录结构和错误码管理
- 📈 **可扩展架构**: 模块化设计，便于功能扩展

## 技术栈

- **后端框架**: Go 1.21 + Gin
- **数据库**: MySQL 8.0
- **缓存**: Redis 7
- **AI模型**: Qwen视觉模型 + DeepSeek对话模型
- **日志**: Zap高性能日志库
- **配置管理**: Viper
- **ORM**: GORM

## 项目结构

```
zhengshi/
├── cmd/server/           # 应用入口
├── internal/             # 内部包
│   ├── config/          # 配置管理
│   ├── handler/         # HTTP处理器
│   ├── service/         # 业务逻辑层
│   ├── repository/      # 数据访问层
│   ├── model/           # 数据模型
│   ├── middleware/      # 中间件
│   ├── utils/           # 工具包
│   └── router/          # 路由配置
├── pkg/client/          # 外部客户端
├── configs/             # 配置文件
├── scripts/             # 脚本文件
├── docs/                # 文档
└── Makefile            # 构建脚本
```

## 快速开始

### 1. 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 7+
- Docker & Docker Compose (可选)

### 2. 克隆项目

```bash
git clone <repository-url>
cd zhengshi
```

### 3. 安装依赖

```bash
make deps
```

### 4. 启动基础服务

使用Docker Compose启动MySQL和Redis：

```bash
make docker-up
```

### 5. 配置应用

复制配置文件并修改：

```bash
cp configs/config.example.yaml configs/config.yaml
```

编辑 `configs/config.yaml`，填入你的API密钥：

```yaml
ai:
  qwen:
    api_key: "your_qwen_api_key"
  deepseek:
    api_key: "your_deepseek_api_key"
```

### 6. 初始化数据库

```bash
make init-db
```

### 7. 运行应用

```bash
make run
```

应用将在 `http://localhost:8080` 启动。

## API文档

### 分析图片

**POST** `/api/v1/image/analyze`

请求体：
```json
{
  "image_url": "https://example.com/image.jpg"
}
```

响应：
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "id": 1,
    "image_url": "https://example.com/image.jpg",
    "qwen_result": "...",
    "deepseek_result": "...",
    "merged_result": "...",
    "process_time": 1500,
    "created_at": "2024-01-01T12:00:00Z"
  },
  "trace_id": "abc123"
}
```

### 获取分析结果

**GET** `/api/v1/image/analysis/{id}`

响应格式同上。

### 健康检查

**GET** `/health`

```json
{
  "status": "ok",
  "message": "Service is running"
}
```

## 开发指南

### 构建命令

```bash
# 构建应用
make build

# 运行测试
make test

# 代码格式化
make fmt

# 代码检查
make lint

# 清理构建文件
make clean
```

### Docker命令

```bash
# 启动服务
make docker-up

# 停止服务
make docker-down

# 查看状态
make docker-status

# 查看日志
make docker-logs
```

## 业务流程

1. **接收请求**: 用户提交图片URL
2. **Qwen分析**: 调用Qwen模型分析图片内容
3. **数据解析**: 格式化解析Qwen响应数据
4. **缓存检查**: 基于解析数据生成缓存键，检查是否有缓存结果
5. **DeepSeek处理**: 如无缓存，调用DeepSeek模型进行深度分析
6. **结果合并**: 合并两个模型的分析结果
7. **数据存储**: 保存完整分析结果到数据库
8. **返回响应**: 返回结构化的分析结果

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1000 | 内部服务器错误 |
| 1001 | 参数错误 |
| 2000 | 图片URL无效 |
| 2001 | 图片处理失败 |
| 2002 | Qwen API调用失败 |
| 2003 | DeepSeek API调用失败 |
| 3000+ | AI模型相关错误 |

## 配置说明

详细配置项请参考 `configs/config.example.yaml`。

## 部署

### 生产构建

```bash
make build-prod
```

### Docker部署

```bash
# 构建镜像
docker build -t zhengshi:latest .

# 运行容器
docker run -d -p 8080:8080 --name zhengshi zhengshi:latest
```

## 监控和日志

- 应用日志输出到stdout或指定文件
- 支持结构化JSON日志格式
- 包含请求追踪ID便于问题排查
- AI模型调用日志单独记录

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

[MIT License](LICENSE)

## 联系方式

如有问题或建议，请提交Issue或联系维护者。
