# S1 多线程拍照搜题脚本

## 概述

根据s1.md文档要求开发的多线程脚本，用于批量测试拍照搜题API服务。

## 功能特性

- ✅ **多线程并发**: 使用10个goroutine同时执行
- ✅ **批量处理**: 从0001到1000循环处理图片
- ✅ **指定图片源**: 使用`http://img.igmdns.com/img/ca{序号}.jpg`格式
- ✅ **日志输出**: 分别记录cache_key和quest_content到独立日志文件
- ✅ **错误日志**: 记录所有失败的图片URL和错误信息到错误日志文件
- ✅ **错误重试**: 失败请求自动重试一次
- ✅ **进度监控**: 实时显示处理进度和状态

## 文件结构

```
├── cmd/s1_script/main.go      # 主脚本文件
├── run_s1_script.sh           # 启动脚本
├── logs/
│   ├── cache_key.log          # Cache Key日志文件
│   ├── quest_content.log      # Quest Content日志文件
│   └── error.log              # 错误日志文件
└── S1_SCRIPT_README.md        # 本说明文档
```

## 使用方法

### 1. 确保API服务运行

首先确保拍照搜题API服务正在运行：

```bash
# 启动主服务
go run cmd/server/main.go
```

服务应该在 `http://localhost:8080` 上运行。

### 2. 运行S1脚本

使用提供的启动脚本：

```bash
# 使用启动脚本（推荐）
./run_s1_script.sh
```

或者直接运行：

```bash
# 直接运行
go run cmd/s1_script/main.go
```

### 3. 查看结果

脚本执行完成后，查看日志文件：

```bash
# 查看Cache Key日志
cat logs/cache_key.log

# 查看Quest Content日志
cat logs/quest_content.log

# 查看错误日志
cat logs/error.log

# 统计处理数量
wc -l logs/cache_key.log logs/quest_content.log logs/error.log
```

## 配置说明

脚本中的主要配置参数：

```go
config := &Config{
    APIBaseURL:      "http://localhost:8080",  // API服务地址
    MaxWorkers:      10,                       // 并发线程数
    StartIndex:      1,                        // 开始序号
    EndIndex:        1000,                     // 结束序号
    ImageURLBase:    "http://img.igmdns.com/img/ca", // 图片URL前缀
    CacheKeyLog:     "logs/cache_key.log",     // Cache Key日志文件
    QuestContentLog: "logs/quest_content.log", // Quest Content日志文件
    ErrorLog:        "logs/error.log",         // 错误日志文件
}
```

## 日志格式

### Cache Key日志格式
```
[2024-06-13 15:30:01] Index: 0001, CacheKey: 判断题下列说法正确的是A正确B错误
[2024-06-13 15:30:02] Index: 0002, CacheKey: 单选题以下哪个选项是正确的A选项AB选项BC选项CD选项D
```

### Quest Content日志格式
```
[2024-06-13 15:30:01] Index: 0001, QuestContent: 下列说法正确的是
[2024-06-13 15:30:02] Index: 0002, QuestContent: 以下哪个选项是正确的
```

### 错误日志格式
```
[2024-06-13 15:30:03] Index: 0012, ImageURL: http://img.igmdns.com/img/ca0012.jpg, Error: API error: code=2000, message=图片不存在,可能是上传失败,请联系管理员处理！
[2024-06-13 15:30:04] Index: 0068, ImageURL: http://img.igmdns.com/img/ca0068.jpg, Error: API error: code=2001, message=图片不标准，请正确拍摄！
```

## 性能特性

- **并发控制**: 使用channel控制并发数量，避免过载
- **错误处理**: 自动重试失败的请求
- **资源管理**: 合理的HTTP客户端超时设置
- **日志同步**: 使用互斥锁确保日志写入的线程安全

## 监控和调试

### 实时监控
```bash
# 监控日志文件增长
tail -f logs/cache_key.log
tail -f logs/quest_content.log

# 统计处理进度
watch "wc -l logs/*.log"
```

### 错误排查
1. 检查API服务是否正常运行
2. 检查网络连接是否正常
3. 查看控制台输出的错误信息
4. 检查日志文件权限

## 注意事项

1. **服务依赖**: 确保主API服务正在运行
2. **网络环境**: 确保能访问指定的图片URL
3. **磁盘空间**: 确保有足够空间存储日志文件
4. **并发限制**: 可根据服务器性能调整并发数量

## 业务需求对应

✅ **多线程执行**: 使用10个goroutine并发处理  
✅ **API调用**: 调用拍照搜题API服务  
✅ **图片处理**: 使用指定图片地址格式  
✅ **范围处理**: 从0001到1000进行  
✅ **Cache Key输出**: 输出到logs/cache_key.log文件  
✅ **Quest Content输出**: 输出到logs/quest_content.log文件  
✅ **每行一次请求**: 每次请求结果占一行  

## 扩展功能

如需修改配置，可以编辑 `cmd/s1_script/main.go` 文件中的配置部分，或者添加命令行参数支持。
