#!/bin/bash

# 严谨的重启脚本 - 只杀死我们自己的服务进程，不影响其他应用
echo "🔄 严谨重启服务..."

PROJECT_NAME="zhengshi"
SERVICE_PORT="8080"
BINARY_NAME="main"

# 函数：安全杀死进程
safe_kill_process() {
    local pid=$1
    local process_name=$2

    if [ -z "$pid" ]; then
        return 0
    fi

    # 检查进程是否存在
    if ! ps -p $pid > /dev/null 2>&1; then
        return 0
    fi

    echo "🛑 停止进程: $process_name (PID: $pid)"

    # 优雅停止
    kill -TERM $pid 2>/dev/null

    # 等待最多5秒
    for i in {1..5}; do
        if ! ps -p $pid > /dev/null 2>&1; then
            echo "✅ 进程已优雅停止: $pid"
            return 0
        fi
        sleep 1
    done

    # 强制停止
    kill -KILL $pid 2>/dev/null
    sleep 1

    if ! ps -p $pid > /dev/null 2>&1; then
        echo "✅ 进程已强制停止: $pid"
    else
        echo "❌ 无法停止进程: $pid"
        return 1
    fi
}

# 1. 通过PID文件停止服务
if [ -f "./logs/app.pid" ]; then
    PID=$(cat ./logs/app.pid)
    if [ -n "$PID" ] && ps -p $PID > /dev/null 2>&1; then
        # 验证这确实是我们的进程
        PROCESS_CMD=$(ps -p $PID -o command= 2>/dev/null)
        if [[ "$PROCESS_CMD" == *"$PROJECT_NAME"* ]] || [[ "$PROCESS_CMD" == *"go run"* ]]; then
            safe_kill_process $PID "zhengshi-service"
        else
            echo "⚠️  PID文件中的进程不是我们的服务，跳过"
        fi
    fi
    rm -f ./logs/app.pid
fi

# 2. 查找并停止项目相关的Go进程
echo "🔍 查找项目相关进程..."
PROJECT_PIDS=$(pgrep -f "go.*run.*cmd/server" 2>/dev/null || true)
if [ -n "$PROJECT_PIDS" ]; then
    echo "发现Go运行进程: $PROJECT_PIDS"
    for pid in $PROJECT_PIDS; do
        PROCESS_CMD=$(ps -p $pid -o command= 2>/dev/null || true)
        if [[ "$PROCESS_CMD" == *"$PROJECT_NAME"* ]]; then
            safe_kill_process $pid "go-run-process"
        fi
    done
else
    echo "未发现Go运行进程"
fi

# 3. 查找并停止编译后的二进制文件
BINARY_PIDS=$(pgrep -x "$BINARY_NAME" 2>/dev/null || true)
if [ -n "$BINARY_PIDS" ]; then
    echo "发现二进制进程: $BINARY_PIDS"
    for pid in $BINARY_PIDS; do
        PROCESS_CMD=$(ps -p $pid -o command= 2>/dev/null || true)
        # 只杀死在我们项目目录中运行的main进程
        if [[ "$PROCESS_CMD" == *"$(pwd)"* ]] || [[ "$PROCESS_CMD" == *"$PROJECT_NAME"* ]]; then
            safe_kill_process $pid "binary-process"
        fi
    done
else
    echo "未发现二进制进程"
fi

# 4. 检查端口占用情况
echo "🔍 检查端口占用..."
if lsof -ti:$SERVICE_PORT >/dev/null 2>&1; then
    echo "⚠️  端口 $SERVICE_PORT 被占用"

    # 只杀死确认是我们项目的进程
    PORT_PIDS=$(lsof -ti:$SERVICE_PORT 2>/dev/null || true)
    KILLED_ANY=false

    for pid in $PORT_PIDS; do
        PROCESS_CMD=$(ps -p $pid -o command= 2>/dev/null || true)
        if [[ "$PROCESS_CMD" == *"$PROJECT_NAME"* ]] || [[ "$PROCESS_CMD" == *"go run"* ]]; then
            echo "🛑 发现端口被我们的进程占用，停止进程: $pid"
            safe_kill_process $pid "port-occupying-process"
            KILLED_ANY=true
        fi
    done

    # 再次检查端口
    if lsof -ti:$SERVICE_PORT >/dev/null 2>&1 && [ "$KILLED_ANY" = false ]; then
        echo "❌ 端口被其他应用占用，无法启动服务"
        lsof -i:$SERVICE_PORT
        exit 1
    fi
else
    echo "✅ 端口 $SERVICE_PORT 可用"
fi

echo "✅ 进程清理完成"

sleep 2

# 5. 启动服务
echo "🚀 启动服务..."
mkdir -p logs

# 清理旧日志（保留最近的日志）
if [ -f "./logs/app.log" ]; then
    mv ./logs/app.log "./logs/app.log.$(date +%Y%m%d_%H%M%S)"
fi

# 启动服务
nohup go run ./cmd/server/main.go > ./logs/app.log 2>&1 &
SERVICE_PID=$!
echo $SERVICE_PID > ./logs/app.pid
echo "📝 服务PID: $SERVICE_PID"

# 验证进程启动
sleep 1
if ! ps -p $SERVICE_PID > /dev/null 2>&1; then
    echo "❌ 服务进程启动失败"
    echo "📋 查看错误日志:"
    tail -20 ./logs/app.log
    exit 1
fi

# 6. 等待服务就绪
echo "⏳ 等待服务就绪..."
for i in {1..15}; do
    if curl -s http://localhost:$SERVICE_PORT/health >/dev/null 2>&1; then
        echo ""
        echo "✅ 服务启动成功！"
        echo "📊 服务信息:"
        echo "   - PID: $SERVICE_PID"
        echo "   - 端口: $SERVICE_PORT"
        echo "   - 日志: ./logs/app.log"
        echo ""
        echo "🔗 可用接口:"
        echo "   - 健康检查: http://localhost:$SERVICE_PORT/health"
        echo "   - 图片分析: http://localhost:$SERVICE_PORT/api/v1/image/analyze"
        echo ""
        echo "📋 查看日志: tail -f ./logs/app.log"
        exit 0
    fi

    # 检查进程是否还在运行
    if ! ps -p $SERVICE_PID > /dev/null 2>&1; then
        echo ""
        echo "❌ 服务进程意外退出"
        echo "📋 查看错误日志:"
        tail -20 ./logs/app.log
        exit 1
    fi

    sleep 1
    echo -n "."
done

echo ""
echo "❌ 服务启动超时"
echo "📋 进程状态: $(ps -p $SERVICE_PID -o pid,ppid,command= 2>/dev/null || echo '进程不存在')"
echo "📋 端口状态: $(lsof -i:$SERVICE_PORT 2>/dev/null || echo '端口未监听')"
echo "📋 查看日志: tail -f ./logs/app.log"
exit 1
