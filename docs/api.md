# API 文档

## 概述

Zhengshi API 提供智能图片分析服务，通过集成 Qwen 视觉模型和 DeepSeek 文本模型，为用户提供全面的图片内容分析和深度解读。

## 基础信息

- **Base URL**: `http://localhost:8080`
- **API Version**: v1
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

所有API响应都遵循统一的格式：

```json
{
  "code": 0,
  "message": "成功",
  "data": {},
  "trace_id": "abc123"
}
```

### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| code | int | 响应码，0表示成功，非0表示错误 |
| message | string | 响应消息 |
| data | object | 响应数据，成功时包含具体数据 |
| trace_id | string | 请求追踪ID，用于问题排查 |

## 错误码

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 0 | 200 | 成功 |
| 1000 | 200 | 内部服务器错误 |
| 1001 | 200 | 参数错误 |
| 1002 | 200 | 未授权 |
| 1003 | 200 | 禁止访问 |
| 1004 | 200 | 资源不存在 |
| 2000 | 200 | 图片URL无效 |
| 2001 | 200 | 图片处理失败 |
| 2002 | 200 | Qwen API调用失败 |
| 2003 | 200 | DeepSeek API调用失败 |
| 2004 | 200 | 缓存操作失败 |
| 2005 | 200 | 数据库操作失败 |

## API 接口

### 1. 健康检查

检查服务运行状态。

**请求**

```
GET /health
```

**响应**

```json
{
  "status": "ok",
  "message": "Service is running"
}
```

### 2. 图片分析

提交图片URL进行智能分析。

**请求**

```
POST /api/v1/image/analyze
```

**请求体**

```json
{
  "image_url": "https://example.com/image.jpg"
}
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| image_url | string | 是 | 图片URL，必须是有效的HTTP/HTTPS链接 |

**响应**

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "id": 1,
    "image_url": "https://example.com/image.jpg",
    "qwen_result": "图片显示了一只可爱的小猫坐在窗台上...",
    "deepseek_result": "基于图像分析，这张图片传达了温馨、宁静的情感...",
    "merged_result": "{\"qwen_analysis\":{...},\"deepseek_result\":\"...\",\"merged_at\":\"2024-01-01T12:00:00Z\"}",
    "process_time": 1500,
    "created_at": "2024-01-01T12:00:00Z"
  },
  "trace_id": "abc123"
}
```

**响应数据字段**

| 字段 | 类型 | 说明 |
|------|------|------|
| id | int | 分析记录ID |
| image_url | string | 原始图片URL |
| qwen_result | string | Qwen模型分析结果 |
| deepseek_result | string | DeepSeek模型分析结果 |
| merged_result | string | 合并后的最终结果（JSON格式） |
| process_time | int | 处理时间（毫秒） |
| created_at | string | 创建时间（ISO 8601格式） |

### 3. 获取分析结果

根据分析记录ID获取详细结果。

**请求**

```
GET /api/v1/image/analysis/{id}
```

**路径参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | int | 是 | 分析记录ID |

**响应**

响应格式与图片分析接口相同。

**错误响应示例**

```json
{
  "code": 1004,
  "message": "资源不存在",
  "trace_id": "def456"
}
```

## 使用示例

### cURL 示例

**分析图片**

```bash
curl -X POST http://localhost:8080/api/v1/image/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/sample.jpg"
  }'
```

**获取分析结果**

```bash
curl -X GET http://localhost:8080/api/v1/image/analysis/1
```

### JavaScript 示例

```javascript
// 分析图片
async function analyzeImage(imageUrl) {
  const response = await fetch('/api/v1/image/analyze', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      image_url: imageUrl
    })
  });
  
  const result = await response.json();
  return result;
}

// 获取分析结果
async function getAnalysisResult(id) {
  const response = await fetch(`/api/v1/image/analysis/${id}`);
  const result = await response.json();
  return result;
}
```

### Python 示例

```python
import requests
import json

# 分析图片
def analyze_image(image_url):
    url = "http://localhost:8080/api/v1/image/analyze"
    payload = {"image_url": image_url}
    headers = {"Content-Type": "application/json"}
    
    response = requests.post(url, data=json.dumps(payload), headers=headers)
    return response.json()

# 获取分析结果
def get_analysis_result(analysis_id):
    url = f"http://localhost:8080/api/v1/image/analysis/{analysis_id}"
    response = requests.get(url)
    return response.json()
```

## 业务流程说明

1. **提交分析请求**: 客户端提交包含图片URL的分析请求
2. **参数验证**: 服务器验证图片URL的有效性
3. **创建分析记录**: 在数据库中创建初始分析记录
4. **Qwen模型分析**: 调用Qwen视觉模型分析图片内容
5. **数据解析**: 解析和格式化Qwen的响应数据
6. **缓存检查**: 基于解析数据生成缓存键，检查是否存在缓存结果
7. **DeepSeek处理**: 如果没有缓存，调用DeepSeek模型进行深度分析
8. **结果合并**: 将两个模型的分析结果进行合并
9. **数据存储**: 更新数据库中的分析记录
10. **返回结果**: 向客户端返回完整的分析结果

## 性能和限制

- **请求频率**: 建议每秒不超过10次请求
- **图片大小**: 支持的图片大小上限为10MB
- **超时时间**: API请求超时时间为60秒
- **缓存时间**: DeepSeek结果缓存24小时
- **并发处理**: 支持最大100个并发请求

## 注意事项

1. **图片URL要求**: 必须是公开可访问的HTTP/HTTPS链接
2. **支持格式**: 支持JPG、PNG、GIF、WebP等常见图片格式
3. **网络访问**: 服务器需要能够访问提供的图片URL
4. **API密钥**: 需要配置有效的Qwen和DeepSeek API密钥
5. **错误处理**: 建议客户端实现适当的错误处理和重试机制
