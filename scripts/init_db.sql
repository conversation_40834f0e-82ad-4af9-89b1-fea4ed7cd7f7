-- 创建数据库
CREATE DATABASE IF NOT EXISTS t_solve_go_api CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE t_solve_go_api;

-- 图片分析表
CREATE TABLE IF NOT EXISTS image_analysis (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    image_url VARCHAR(500) NOT NULL COMMENT '图片URL',
    qwen_result TEXT COMMENT 'Qwen模型分析结果',
    qwen_parsed TEXT COMMENT 'Qwen结果解析数据',
    deepseek_result TEXT COMMENT 'DeepSeek模型处理结果',
    merged_result TEXT COMMENT '合并后的最终结果',
    cache_key VARCHAR(255) COMMENT '缓存键名',
    process_time BIGINT DEFAULT 0 COMMENT '处理时间(毫秒)',
    status VARCHAR(50) DEFAULT 'processing' COMMENT '状态: processing, completed, failed',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    INDEX idx_image_url (image_url),
    INDEX idx_cache_key (cache_key),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片分析记录表';

-- AI模型调用日志表
CREATE TABLE IF NOT EXISTS ai_model_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    analysis_id BIGINT UNSIGNED COMMENT '关联的分析记录ID',
    model_type VARCHAR(50) NOT NULL COMMENT '模型类型: qwen, deepseek',
    model_name VARCHAR(100) COMMENT '具体模型名称',
    request_data TEXT COMMENT '请求数据',
    response_data TEXT COMMENT '响应数据',
    token_usage INT DEFAULT 0 COMMENT 'Token使用量',
    duration BIGINT DEFAULT 0 COMMENT '请求耗时(毫秒)',
    status VARCHAR(50) DEFAULT 'success' COMMENT '状态: success, failed',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    INDEX idx_analysis_id (analysis_id),
    INDEX idx_model_type (model_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at),
    
    FOREIGN KEY (analysis_id) REFERENCES image_analysis(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI模型调用日志表';

-- 缓存记录表
CREATE TABLE IF NOT EXISTS cache_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    cache_key VARCHAR(255) NOT NULL UNIQUE COMMENT '缓存键',
    data TEXT COMMENT '缓存数据',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    UNIQUE INDEX uk_cache_key (cache_key),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='缓存记录表';

-- 插入一些示例数据（可选）
-- INSERT INTO image_analysis (image_url, status) VALUES 
-- ('https://example.com/test1.jpg', 'processing'),
-- ('https://example.com/test2.jpg', 'completed');
