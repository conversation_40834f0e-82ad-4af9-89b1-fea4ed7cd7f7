version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: zhengshi_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: zhengshi
      MYSQL_USER: zhengs<PERSON>
      MYSQL_PASSWORD: zhengshi123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - zhengshi_network

  redis:
    image: redis:7-alpine
    container_name: zhengshi_redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - zhengshi_network

  # 可选：添加Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: zhengshi_redis_commander
    restart: always
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - zhengshi_network

  # 可选：添加MySQL管理界面
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: zhengshi_phpmyadmin
    restart: always
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: password
    ports:
      - "8082:80"
    depends_on:
      - mysql
    networks:
      - zhengshi_network

volumes:
  mysql_data:
  redis_data:

networks:
  zhengshi_network:
    driver: bridge
