package repository

import (
	"context"
	"zhengshi/internal/model"
	"gorm.io/gorm"
)

// ImageRepository 图片仓储接口
type ImageRepository interface {
	Create(ctx context.Context, analysis *model.ImageAnalysis) error
	Update(ctx context.Context, analysis *model.ImageAnalysis) error
	GetByID(ctx context.Context, id uint) (*model.ImageAnalysis, error)
	GetByImageURL(ctx context.Context, imageURL string) (*model.ImageAnalysis, error)
	List(ctx context.Context, offset, limit int) ([]*model.ImageAnalysis, error)
	Delete(ctx context.Context, id uint) error
}

// imageRepository 图片仓储实现
type imageRepository struct {
	db *gorm.DB
}

// NewImageRepository 创建图片仓储
func NewImageRepository(db *gorm.DB) ImageRepository {
	return &imageRepository{db: db}
}

// Create 创建分析记录
func (r *imageRepository) Create(ctx context.Context, analysis *model.ImageAnalysis) error {
	return r.db.WithContext(ctx).Create(analysis).Error
}

// Update 更新分析记录
func (r *imageRepository) Update(ctx context.Context, analysis *model.ImageAnalysis) error {
	return r.db.WithContext(ctx).Save(analysis).Error
}

// GetByID 根据ID获取分析记录
func (r *imageRepository) GetByID(ctx context.Context, id uint) (*model.ImageAnalysis, error) {
	var analysis model.ImageAnalysis
	err := r.db.WithContext(ctx).First(&analysis, id).Error
	if err != nil {
		return nil, err
	}
	return &analysis, nil
}

// GetByImageURL 根据图片URL获取分析记录
func (r *imageRepository) GetByImageURL(ctx context.Context, imageURL string) (*model.ImageAnalysis, error) {
	var analysis model.ImageAnalysis
	err := r.db.WithContext(ctx).Where("image_url = ?", imageURL).First(&analysis).Error
	if err != nil {
		return nil, err
	}
	return &analysis, nil
}

// List 获取分析记录列表
func (r *imageRepository) List(ctx context.Context, offset, limit int) ([]*model.ImageAnalysis, error) {
	var analyses []*model.ImageAnalysis
	err := r.db.WithContext(ctx).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&analyses).Error
	return analyses, err
}

// Delete 删除分析记录
func (r *imageRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.ImageAnalysis{}, id).Error
}
