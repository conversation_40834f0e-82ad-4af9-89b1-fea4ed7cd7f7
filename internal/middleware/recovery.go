package middleware

import (
	"net/http"

	"zhengshi/internal/model"
	"zhengshi/internal/utils"

	"github.com/gin-gonic/gin"
)

// Recovery 恢复中间件
func Recovery(logger utils.Logger) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		// 记录panic日志
		logger.Error("Panic recovered", nil,
			"panic", recovered,
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"client_ip", c.ClientIP(),
		)

		// 返回统一错误响应
		c.JSON(http.StatusInternalServerError, model.APIResponse{
			Code:    utils.CodeInternalError,
			Message: utils.GetErrorMessage(utils.CodeInternalError),
		})
	})
}
