package model

import (
	"time"
	"gorm.io/gorm"
)

// ImageAnalysis 图片分析数据库实体
type ImageAnalysis struct {
	ID             uint           `gorm:"primarykey" json:"id"`
	ImageURL       string         `gorm:"type:varchar(500);not null;index" json:"image_url"`
	QwenResult     string         `gorm:"type:text" json:"qwen_result"`
	QwenParsed     string         `gorm:"type:text" json:"qwen_parsed"`
	DeepSeekResult string         `gorm:"type:text" json:"deepseek_result"`
	MergedResult   string         `gorm:"type:text" json:"merged_result"`
	CacheKey       string         `gorm:"type:varchar(255);index" json:"cache_key"`
	ProcessTime    int64          `gorm:"type:bigint" json:"process_time"` // 处理时间（毫秒）
	Status         string         `gorm:"type:varchar(50);default:'processing'" json:"status"` // processing, completed, failed
	ErrorMessage   string         `gorm:"type:text" json:"error_message,omitempty"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TableName 指定表名
func (ImageAnalysis) TableName() string {
	return "image_analysis"
}

// AIModelLog AI模型调用日志
type AIModelLog struct {
	ID           uint           `gorm:"primarykey" json:"id"`
	AnalysisID   uint           `gorm:"index" json:"analysis_id"`
	ModelType    string         `gorm:"type:varchar(50);not null" json:"model_type"` // qwen, deepseek
	ModelName    string         `gorm:"type:varchar(100)" json:"model_name"`
	RequestData  string         `gorm:"type:text" json:"request_data"`
	ResponseData string         `gorm:"type:text" json:"response_data"`
	TokenUsage   int            `gorm:"type:int" json:"token_usage"`
	Duration     int64          `gorm:"type:bigint" json:"duration"` // 请求耗时（毫秒）
	Status       string         `gorm:"type:varchar(50)" json:"status"` // success, failed
	ErrorMessage string         `gorm:"type:text" json:"error_message,omitempty"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TableName 指定表名
func (AIModelLog) TableName() string {
	return "ai_model_logs"
}

// CacheRecord 缓存记录
type CacheRecord struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CacheKey  string         `gorm:"type:varchar(255);uniqueIndex" json:"cache_key"`
	Data      string         `gorm:"type:text" json:"data"`
	ExpiresAt time.Time      `json:"expires_at"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TableName 指定表名
func (CacheRecord) TableName() string {
	return "cache_records"
}
