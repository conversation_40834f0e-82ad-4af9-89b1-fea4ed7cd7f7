package model

import "time"

// APIResponse 统一API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	TraceID string      `json:"trace_id,omitempty"`
}

// ImageAnalysisResponse 图片分析响应结构
type ImageAnalysisResponse struct {
	ID          uint      `json:"id"`
	ImageURL    string    `json:"image_url"`
	QwenResult  string    `json:"qwen_result"`
	DeepSeekResult string `json:"deepseek_result"`
	MergedResult string  `json:"merged_result"`
	ProcessTime  int64   `json:"process_time"` // 处理时间（毫秒）
	CreatedAt   time.Time `json:"created_at"`
}

// QwenDashScopeResponse DashScope格式的Qwen响应结构
type QwenDashScopeResponse struct {
	Output    QwenOutput    `json:"output"`
	Usage     QwenUsage     `json:"usage"`
	RequestID string        `json:"request_id"`
}

// QwenOutput Qwen输出结构
type QwenOutput struct {
	Text         string       `json:"text"`
	FinishReason string       `json:"finish_reason"`
	Choices      []QwenChoice `json:"choices,omitempty"`
}

// QwenChoice Qwen选择结构
type QwenChoice struct {
	FinishReason string      `json:"finish_reason"`
	Message      QwenMessage `json:"message"`
}

// QwenMessage Qwen消息结构（用于响应）
type QwenMessage struct {
	Role    string                    `json:"role"`
	Content []QwenDashScopeContent    `json:"content"`
}

// QwenUsage Qwen使用情况
type QwenUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// DeepSeekResponse DeepSeek模型响应结构
type DeepSeekResponse struct {
	ID      string            `json:"id"`
	Object  string            `json:"object"`
	Created int64             `json:"created"`
	Model   string            `json:"model"`
	Choices []DeepSeekChoice  `json:"choices"`
	Usage   DeepSeekUsage     `json:"usage"`
}

// DeepSeekChoice DeepSeek选择结构
type DeepSeekChoice struct {
	Index        int               `json:"index"`
	Message      DeepSeekMessage   `json:"message"`
	FinishReason string            `json:"finish_reason"`
}

// DeepSeekUsage DeepSeek使用情况
type DeepSeekUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// ParsedQwenData 解析后的Qwen数据结构
type ParsedQwenData struct {
	Description string            `json:"description"`
	Objects     []string          `json:"objects"`
	Scene       string            `json:"scene"`
	Colors      []string          `json:"colors"`
	Emotions    string            `json:"emotions"`
	Keywords    []string          `json:"keywords"`
	Metadata    map[string]interface{} `json:"metadata"`
}
