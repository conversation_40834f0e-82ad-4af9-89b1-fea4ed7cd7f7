package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"sync"
	"time"
)

// APIRequest 请求结构体
type APIRequest struct {
	ImageURL string `json:"image_url"`
}

// ProcessContext API响应中的ProcessContext结构体
type ProcessContext struct {
	TraceID      string            `json:"trace_id"`
	ImageURL     string            `json:"image_url"`
	StartTime    time.Time         `json:"start_time"`
	Status       string            `json:"status"`
	CurrentStep  string            `json:"current_step"`
	QwenRawData  string            `json:"qwen_raw_data"`
	CacheKey     string            `json:"cache_key"`
	QuestType    string            `json:"quest_type"`
	QuestOption  map[string]string `json:"quest_option"`
	QuestContent string            `json:"quest_content"`
}

// APIResponse API响应结构体
type APIResponse struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Data    *ProcessContext `json:"data"`
	TraceID string          `json:"trace_id"`
}

// Config 配置
type Config struct {
	APIBaseURL      string
	MaxWorkers      int
	StartIndex      int
	EndIndex        int
	ImageURLBase    string
	CacheKeyLog     string
	QuestContentLog string
	ErrorLog        string
}

// Logger 日志记录器
type Logger struct {
	cacheKeyFile     *os.File
	questContentFile *os.File
	errorFile        *os.File
	mutex            sync.Mutex
}

// NewLogger 创建新的日志记录器
func NewLogger(cacheKeyPath, questContentPath, errorPath string) (*Logger, error) {
	// 创建或打开cache_key日志文件
	cacheKeyFile, err := os.OpenFile(cacheKeyPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to open cache key log file: %v", err)
	}

	// 创建或打开quest_content日志文件
	questContentFile, err := os.OpenFile(questContentPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		cacheKeyFile.Close()
		return nil, fmt.Errorf("failed to open quest content log file: %v", err)
	}

	// 创建或打开error日志文件
	errorFile, err := os.OpenFile(errorPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		cacheKeyFile.Close()
		questContentFile.Close()
		return nil, fmt.Errorf("failed to open error log file: %v", err)
	}

	return &Logger{
		cacheKeyFile:     cacheKeyFile,
		questContentFile: questContentFile,
		errorFile:        errorFile,
	}, nil
}

// LogCacheKey 记录cache_key
func (l *Logger) LogCacheKey(index int, cacheKey string) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	logLine := fmt.Sprintf("[%s] Index: %04d, CacheKey: %s\n", 
		time.Now().Format("2006-01-02 15:04:05"), index, cacheKey)
	
	_, err := l.cacheKeyFile.WriteString(logLine)
	if err != nil {
		return err
	}
	
	return l.cacheKeyFile.Sync()
}

// LogQuestContent 记录quest_content
func (l *Logger) LogQuestContent(index int, questContent string) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	logLine := fmt.Sprintf("[%s] Index: %04d, QuestContent: %s\n", 
		time.Now().Format("2006-01-02 15:04:05"), index, questContent)
	
	_, err := l.questContentFile.WriteString(logLine)
	if err != nil {
		return err
	}
	
	return l.questContentFile.Sync()
}

// LogError 记录错误信息
func (l *Logger) LogError(index int, imageURL string, errorMsg string) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	logLine := fmt.Sprintf("[%s] Index: %04d, ImageURL: %s, Error: %s\n",
		time.Now().Format("2006-01-02 15:04:05"), index, imageURL, errorMsg)

	_, err := l.errorFile.WriteString(logLine)
	if err != nil {
		return err
	}

	return l.errorFile.Sync()
}

// Close 关闭日志文件
func (l *Logger) Close() {
	if l.cacheKeyFile != nil {
		l.cacheKeyFile.Close()
	}
	if l.questContentFile != nil {
		l.questContentFile.Close()
	}
	if l.errorFile != nil {
		l.errorFile.Close()
	}
}

// Worker 工作任务
type Worker struct {
	id     int
	config *Config
	logger *Logger
	client *http.Client
}

// NewWorker 创建新的工作器
func NewWorker(id int, config *Config, logger *Logger) *Worker {
	return &Worker{
		id:     id,
		config: config,
		logger: logger,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// ProcessImage 处理单个图片
func (w *Worker) ProcessImage(index int) error {
	// 构造图片URL
	imageURL := fmt.Sprintf("%s%04d.jpg", w.config.ImageURLBase, index)
	
	log.Printf("Worker %d processing index %d: %s", w.id, index, imageURL)

	// 构造请求
	reqBody := APIRequest{
		ImageURL: imageURL,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	// 发送HTTP请求
	apiURL := fmt.Sprintf("%s/api/v1/image/analyze", w.config.APIBaseURL)
	resp, err := w.client.Post(apiURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return fmt.Errorf("failed to unmarshal response: %v", err)
	}

	// 检查API响应状态
	if apiResp.Code != 0 {
		errorMsg := fmt.Sprintf("API error: code=%d, message=%s", apiResp.Code, apiResp.Message)
		// 记录错误到错误日志文件
		if err := w.logger.LogError(index, imageURL, errorMsg); err != nil {
			log.Printf("Worker %d failed to log error for index %d: %v", w.id, index, err)
		}
		return fmt.Errorf(errorMsg)
	}

	if apiResp.Data == nil {
		errorMsg := "API response data is nil"
		// 记录错误到错误日志文件
		if err := w.logger.LogError(index, imageURL, errorMsg); err != nil {
			log.Printf("Worker %d failed to log error for index %d: %v", w.id, index, err)
		}
		return fmt.Errorf(errorMsg)
	}

	// 记录cache_key到日志文件
	if apiResp.Data.CacheKey != "" {
		if err := w.logger.LogCacheKey(index, apiResp.Data.CacheKey); err != nil {
			log.Printf("Worker %d failed to log cache key for index %d: %v", w.id, index, err)
		}
	}

	// 记录quest_content到日志文件
	if apiResp.Data.QuestContent != "" {
		if err := w.logger.LogQuestContent(index, apiResp.Data.QuestContent); err != nil {
			log.Printf("Worker %d failed to log quest content for index %d: %v", w.id, index, err)
		}
	}

	log.Printf("Worker %d completed index %d successfully", w.id, index)
	return nil
}

func main() {
	// 配置
	config := &Config{
		APIBaseURL:      "http://localhost:8080",
		MaxWorkers:      10,
		StartIndex:      1,
		EndIndex:        1000, // 完整范围：从0001到1000
		ImageURLBase:    "http://img.igmdns.com/img/ca",
		CacheKeyLog:     "logs/cache_key.log",
		QuestContentLog: "logs/quest_content.log",
		ErrorLog:        "logs/error.log",
	}

	log.Printf("Starting S1 script with config: %+v", config)

	// 确保logs目录存在
	if err := os.MkdirAll("logs", 0755); err != nil {
		log.Fatalf("Failed to create logs directory: %v", err)
	}

	// 创建日志记录器
	logger, err := NewLogger(config.CacheKeyLog, config.QuestContentLog, config.ErrorLog)
	if err != nil {
		log.Fatalf("Failed to create logger: %v", err)
	}
	defer logger.Close()

	// 创建任务通道
	taskChan := make(chan int, config.EndIndex-config.StartIndex+1)
	
	// 填充任务
	for i := config.StartIndex; i <= config.EndIndex; i++ {
		taskChan <- i
	}
	close(taskChan)

	// 创建等待组
	var wg sync.WaitGroup

	// 启动工作器
	for i := 1; i <= config.MaxWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			
			worker := NewWorker(workerID, config, logger)
			
			for index := range taskChan {
				// 处理任务，如果失败则重试一次
				if err := worker.ProcessImage(index); err != nil {
					log.Printf("Worker %d failed to process index %d: %v, retrying...", workerID, index, err)
					
					// 重试一次
					time.Sleep(1 * time.Second)
					if err := worker.ProcessImage(index); err != nil {
						log.Printf("Worker %d failed to process index %d after retry: %v", workerID, index, err)
					}
				}
				
				// 添加小延迟避免过于频繁的请求
				time.Sleep(100 * time.Millisecond)
			}
			
			log.Printf("Worker %d finished", workerID)
		}(i)
	}

	log.Printf("All workers started, waiting for completion...")
	
	// 等待所有工作器完成
	wg.Wait()
	
	log.Printf("S1 script completed successfully!")
}
