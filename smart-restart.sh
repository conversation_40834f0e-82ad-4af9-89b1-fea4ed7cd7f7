#!/bin/bash

# 智能重启脚本 - 严谨且安全，不会误杀其他应用
set -e

PROJECT_NAME="zhengshi"
SERVICE_PORT="8080"
LOG_DIR="./logs"
PID_FILE="$LOG_DIR/app.pid"
LOG_FILE="$LOG_DIR/app.log"

echo "🔄 智能重启 $PROJECT_NAME 服务..."

# 创建日志目录
mkdir -p "$LOG_DIR"

# 函数：安全停止进程
safe_stop_process() {
    local pid=$1
    local name=$2
    
    if [ -z "$pid" ] || ! kill -0 "$pid" 2>/dev/null; then
        return 0
    fi
    
    echo "🛑 停止进程: $name (PID: $pid)"
    
    # 发送TERM信号
    if kill -TERM "$pid" 2>/dev/null; then
        # 等待进程优雅退出
        for i in {1..5}; do
            if ! kill -0 "$pid" 2>/dev/null; then
                echo "✅ 进程已优雅停止: $pid"
                return 0
            fi
            sleep 1
        done
        
        # 强制停止
        if kill -KILL "$pid" 2>/dev/null; then
            echo "✅ 进程已强制停止: $pid"
        fi
    fi
}

# 1. 通过PID文件停止服务
if [ -f "$PID_FILE" ]; then
    OLD_PID=$(cat "$PID_FILE" 2>/dev/null || echo "")
    if [ -n "$OLD_PID" ] && kill -0 "$OLD_PID" 2>/dev/null; then
        # 验证是否是我们的进程
        PROCESS_INFO=$(ps -p "$OLD_PID" -o command= 2>/dev/null || echo "")
        if [[ "$PROCESS_INFO" == *"$PROJECT_NAME"* ]] || [[ "$PROCESS_INFO" == *"go run"* ]]; then
            safe_stop_process "$OLD_PID" "zhengshi-service"
        else
            echo "⚠️  PID文件中的进程不是我们的服务"
        fi
    fi
    rm -f "$PID_FILE"
fi

# 2. 查找并停止相关进程
echo "🔍 查找相关进程..."

# 查找go run进程
GO_PIDS=$(ps aux | grep "go.*run.*cmd/server" | grep -v grep | awk '{print $2}' || true)
for pid in $GO_PIDS; do
    PROCESS_INFO=$(ps -p "$pid" -o command= 2>/dev/null || echo "")
    if [[ "$PROCESS_INFO" == *"$PROJECT_NAME"* ]]; then
        safe_stop_process "$pid" "go-run-process"
    fi
done

# 3. 检查端口占用
echo "🔍 检查端口 $SERVICE_PORT..."
PORT_PIDS=$(lsof -ti:$SERVICE_PORT 2>/dev/null || true)

if [ -n "$PORT_PIDS" ]; then
    echo "⚠️  端口 $SERVICE_PORT 被占用"
    
    for pid in $PORT_PIDS; do
        PROCESS_INFO=$(ps -p "$pid" -o command= 2>/dev/null || echo "")
        echo "   进程 $pid: $PROCESS_INFO"
        
        # 只停止我们自己的进程
        if [[ "$PROCESS_INFO" == *"$PROJECT_NAME"* ]] || [[ "$PROCESS_INFO" == *"go run"* ]] || [[ "$PROCESS_INFO" == *"go-build"* ]] || [[ "$PROCESS_INFO" == *"exe/main"* ]]; then
            safe_stop_process "$pid" "port-process"
        fi
    done
    
    # 再次检查端口
    sleep 1
    if lsof -ti:$SERVICE_PORT >/dev/null 2>&1; then
        echo "❌ 端口仍被其他应用占用，请手动处理"
        lsof -i:$SERVICE_PORT
        exit 1
    fi
fi

echo "✅ 进程清理完成"

# 4. 启动新服务
echo "🚀 启动新服务..."

# 备份旧日志
if [ -f "$LOG_FILE" ]; then
    mv "$LOG_FILE" "${LOG_FILE}.$(date +%Y%m%d_%H%M%S)"
fi

# 启动服务
nohup go run ./cmd/server/main.go > "$LOG_FILE" 2>&1 &
NEW_PID=$!
echo "$NEW_PID" > "$PID_FILE"

echo "📝 新服务PID: $NEW_PID"

# 5. 验证启动
echo "⏳ 验证服务启动..."

# 检查进程是否存在
sleep 2
if ! kill -0 "$NEW_PID" 2>/dev/null; then
    echo "❌ 服务进程启动失败"
    echo "📋 错误日志:"
    tail -20 "$LOG_FILE" 2>/dev/null || echo "无法读取日志文件"
    exit 1
fi

# 检查服务是否响应
for i in {1..15}; do
    if curl -s "http://localhost:$SERVICE_PORT/health" >/dev/null 2>&1; then
        echo ""
        echo "✅ 服务启动成功！"
        echo ""
        echo "📊 服务信息:"
        echo "   - PID: $NEW_PID"
        echo "   - 端口: $SERVICE_PORT"
        echo "   - 日志: $LOG_FILE"
        echo ""
        echo "🔗 可用接口:"
        echo "   - 健康检查: http://localhost:$SERVICE_PORT/health"
        echo "   - 图片分析: http://localhost:$SERVICE_PORT/api/v1/image/analyze"
        echo ""
        echo "📋 查看日志: tail -f $LOG_FILE"
        exit 0
    fi
    
    # 检查进程是否还在运行
    if ! kill -0 "$NEW_PID" 2>/dev/null; then
        echo ""
        echo "❌ 服务进程意外退出"
        echo "📋 错误日志:"
        tail -20 "$LOG_FILE" 2>/dev/null || echo "无法读取日志文件"
        exit 1
    fi
    
    sleep 1
    echo -n "."
done

echo ""
echo "❌ 服务启动超时"
echo "📋 进程状态: $(ps -p $NEW_PID -o pid,command= 2>/dev/null || echo '进程不存在')"
echo "📋 端口状态: $(lsof -i:$SERVICE_PORT 2>/dev/null || echo '端口未监听')"
echo "📋 查看日志: tail -f $LOG_FILE"
exit 1
