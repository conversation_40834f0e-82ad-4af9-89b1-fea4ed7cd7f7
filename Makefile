.PHONY: build run test clean docker-up docker-down deps fmt lint

# 应用名称
APP_NAME := zhengshi
BUILD_DIR := ./build
MAIN_FILE := ./cmd/server/main.go

# Go相关变量
GO := go
GOFMT := gofmt
GOLINT := golangci-lint

# 构建应用
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GO) build -o $(BUILD_DIR)/$(APP_NAME) $(MAIN_FILE)
	@echo "Build completed: $(BUILD_DIR)/$(APP_NAME)"

# 运行应用
run:
	@echo "Running $(APP_NAME)..."
	$(GO) run $(MAIN_FILE)

# 运行测试
test:
	@echo "Running tests..."
	$(GO) test -v ./...

# 运行测试并生成覆盖率报告
test-coverage:
	@echo "Running tests with coverage..."
	$(GO) test -v -coverprofile=coverage.out ./...
	$(GO) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# 清理构建文件
clean:
	@echo "Cleaning build files..."
	@rm -rf $(BUILD_DIR)
	@rm -f coverage.out coverage.html
	@echo "Clean completed"

# 安装依赖
deps:
	@echo "Installing dependencies..."
	$(GO) mod tidy
	$(GO) mod download
	@echo "Dependencies installed"

# 格式化代码
fmt:
	@echo "Formatting code..."
	$(GOFMT) -s -w .
	@echo "Code formatted"

# 代码检查
lint:
	@echo "Running linter..."
	$(GOLINT) run
	@echo "Linting completed"

# 启动Docker服务
docker-up:
	@echo "Starting Docker services..."
	cd scripts && docker-compose up -d
	@echo "Docker services started"

# 停止Docker服务
docker-down:
	@echo "Stopping Docker services..."
	cd scripts && docker-compose down
	@echo "Docker services stopped"

# 查看Docker服务状态
docker-status:
	@echo "Docker services status:"
	cd scripts && docker-compose ps

# 查看Docker服务日志
docker-logs:
	@echo "Docker services logs:"
	cd scripts && docker-compose logs -f

# 初始化数据库
init-db:
	@echo "Initializing database..."
	mysql -h localhost -P 3306 -u root -p < scripts/init_db.sql
	@echo "Database initialized"

# 开发环境设置
dev-setup: deps docker-up
	@echo "Development environment setup completed"
	@echo "Please update configs/config.yaml with your API keys"

# 生产构建
build-prod:
	@echo "Building for production..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GO) build -ldflags="-w -s" -o $(BUILD_DIR)/$(APP_NAME) $(MAIN_FILE)
	@echo "Production build completed: $(BUILD_DIR)/$(APP_NAME)"

# 帮助信息
help:
	@echo "Available commands:"
	@echo "  build        - Build the application"
	@echo "  run          - Run the application"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage report"
	@echo "  clean        - Clean build files"
	@echo "  deps         - Install dependencies"
	@echo "  fmt          - Format code"
	@echo "  lint         - Run linter"
	@echo "  docker-up    - Start Docker services"
	@echo "  docker-down  - Stop Docker services"
	@echo "  docker-status- Show Docker services status"
	@echo "  docker-logs  - Show Docker services logs"
	@echo "  init-db      - Initialize database"
	@echo "  dev-setup    - Setup development environment"
	@echo "  build-prod   - Build for production"
	@echo "  help         - Show this help message"
