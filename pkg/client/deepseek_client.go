package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"zhengshi/internal/config"
	"zhengshi/internal/model"
	"zhengshi/internal/utils"
)

// DeepSeekClient DeepSeek客户端接口
type DeepSeekClient interface {
	ProcessData(ctx context.Context, data *model.ParsedQwenData) (string, error)
}

// deepSeekClient DeepSeek客户端实现
type deepSeekClient struct {
	config     config.DeepSeekConfig
	httpClient *http.Client
	logger     utils.Logger
}

// NewDeepSeekClient 创建DeepSeek客户端
func NewDeepSeekClient(cfg config.DeepSeekConfig, logger utils.Logger) DeepSeekClient {
	return &deepSeekClient{
		config: cfg,
		httpClient: &http.Client{
			Timeout: time.Duration(cfg.Timeout) * time.Second,
		},
		logger: logger,
	}
}

// ProcessData 处理数据
func (c *deepSeekClient) ProcessData(ctx context.Context, data *model.ParsedQwenData) (string, error) {
	// 构建提示词
	prompt := c.buildPrompt(data)

	// 构建请求数据
	request := model.DeepSeekRequest{
		Model:       c.config.Model,
		Temperature: 0.7,
		MaxTokens:   2000,
		Stream:      false,
		Messages: []model.DeepSeekMessage{
			{
				Role:    "system",
				Content: "你是一个专业的图像分析助手，能够基于图像分析结果提供深入的见解和建议。",
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
	}

	// 序列化请求数据
	requestBody, err := json.Marshal(request)
	if err != nil {
		c.logger.Error("Failed to marshal DeepSeek request", err)
		return "", fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", c.config.BaseURL, bytes.NewBuffer(requestBody))
	if err != nil {
		c.logger.Error("Failed to create DeepSeek HTTP request", err)
		return "", fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.config.APIKey)

	c.logger.Info("Sending request to DeepSeek API", 
		"url", c.config.BaseURL,
		"model", c.config.Model,
	)

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.logger.Error("DeepSeek API request failed", err)
		return "", fmt.Errorf("DeepSeek API请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.logger.Error("Failed to read DeepSeek response", err)
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		c.logger.Error("DeepSeek API returned error status", nil,
			"status_code", resp.StatusCode,
			"response", string(responseBody),
		)
		return "", fmt.Errorf("DeepSeek API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var deepSeekResponse model.DeepSeekResponse
	if err := json.Unmarshal(responseBody, &deepSeekResponse); err != nil {
		c.logger.Error("Failed to unmarshal DeepSeek response", err, 
			"response", string(responseBody),
		)
		return "", fmt.Errorf("解析DeepSeek响应失败: %w", err)
	}

	// 提取响应内容
	if len(deepSeekResponse.Choices) == 0 {
		c.logger.Error("DeepSeek response has no choices", nil)
		return "", fmt.Errorf("DeepSeek响应中没有选择项")
	}

	content := deepSeekResponse.Choices[0].Message.Content

	c.logger.Info("DeepSeek API request completed successfully",
		"tokens_used", deepSeekResponse.Usage.TotalTokens,
	)

	return content, nil
}

// buildPrompt 构建提示词
func (c *deepSeekClient) buildPrompt(data *model.ParsedQwenData) string {
	dataJSON, _ := json.Marshal(data)
	
	prompt := fmt.Sprintf(`
基于以下图像分析结果，请提供深入的见解和建议：

图像分析数据：
%s

请从以下几个方面进行分析：
1. 图像内容的深层含义和象征意义
2. 可能的应用场景和用途
3. 情感表达和心理影响
4. 艺术价值和美学评价
5. 改进建议和优化方向

请以结构化的JSON格式返回分析结果。
`, string(dataJSON))

	return prompt
}
