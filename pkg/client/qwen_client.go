package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"zhengshi/internal/config"
	"zhengshi/internal/model"
	"zhengshi/internal/utils"
)

// QwenClient Qwen客户端接口
type QwenClient interface {
	AnalyzeImage(ctx context.Context, imageURL string) (string, error)
}

// qwenClient Qwen客户端实现
type qwenClient struct {
	config     config.QwenConfig
	httpClient *http.Client
	logger     utils.Logger
}

// NewQwenClient 创建Qwen客户端
func NewQwenClient(cfg config.QwenConfig, logger utils.Logger) QwenClient {
	return &qwenClient{
		config: cfg,
		httpClient: &http.Client{
			Timeout: time.Duration(cfg.Timeout) * time.Second,
		},
		logger: logger,
	}
}

// AnalyzeImage 分析图片 - 按照S1文档DashScope格式实现
func (c *qwenClient) AnalyzeImage(ctx context.Context, imageURL string) (string, error) {
	// 构建DashScope格式的请求数据
	request := model.QwenDashScopeRequest{
		Input: model.QwenInput{
			Messages: []model.QwenDashScopeMessage{
				{
					Role: "system",
					Content: []model.QwenDashScopeContent{
						{
							Text: "严格标准返回json格式。示例{\"qutext\":\"(题目类型)(序号)(题目正文)\",\"options\":{\"所有选项\"}}",
						},
					},
				},
				{
					Role: "user",
					Content: []model.QwenDashScopeContent{
						{
							Image: imageURL,
						},
						{
							Text: "精准且完整的识别问题,严格按照要求输出的完整json,其中的options字段的格式必须正确,选项字母为键，选项内容为值",
						},
					},
				},
			},
		},
		Model: "qwen-vl-plus",
		Parameters: model.QwenParameters{
			PresencePenalty:   1,
			RepetitionPenalty: 1,
			ResponseFormat: model.QwenResponseFormat{
				Type: "json_object",
			},
			Temperature: 0.2,
			TopK:        1,
			TopP:        0.01,
		},
	}

	// 序列化请求数据
	requestBody, err := json.Marshal(request)
	if err != nil {
		c.logger.Error("Failed to marshal Qwen request", err, "image_url", imageURL)
		return "", fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", c.config.BaseURL, bytes.NewBuffer(requestBody))
	if err != nil {
		c.logger.Error("Failed to create Qwen HTTP request", err, "image_url", imageURL)
		return "", fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置DashScope请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.config.APIKey)

	c.logger.Info("Sending request to Qwen API", 
		"url", c.config.BaseURL,
		"model", c.config.Model,
		"image_url", imageURL,
	)

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.logger.Error("Qwen API request failed", err, "image_url", imageURL)
		return "", fmt.Errorf("Qwen API请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.logger.Error("Failed to read Qwen response", err, "image_url", imageURL)
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		c.logger.Error("Qwen API returned error status", nil,
			"status_code", resp.StatusCode,
			"response", string(responseBody),
			"image_url", imageURL,
		)
		return "", fmt.Errorf("Qwen API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 验证响应格式（简单检查，确保是有效的JSON响应）
	var qwenResponse model.QwenDashScopeResponse
	if err := json.Unmarshal(responseBody, &qwenResponse); err != nil {
		c.logger.Error("Failed to unmarshal Qwen DashScope response", err,
			"response", string(responseBody),
			"image_url", imageURL,
		)
		return "", fmt.Errorf("解析Qwen DashScope响应失败: %w", err)
	}

	// 基本验证：确保响应包含必要的字段
	if len(qwenResponse.Output.Choices) == 0 {
		c.logger.Error("Qwen response has no choices", nil, "image_url", imageURL)
		return "", fmt.Errorf("Qwen响应中没有选择项")
	}

	c.logger.Info("Qwen DashScope API request completed successfully",
		"image_url", imageURL,
		"tokens_used", qwenResponse.Usage.TotalTokens,
		"request_id", qwenResponse.RequestID,
		"response_size", len(responseBody),
	)

	// 返回完整的原始响应JSON字符串
	return string(responseBody), nil
}
